# 将主要服务暴露为 NodePort 类型
apiVersion: v1
kind: Service
metadata:
  name: frontend-proxy
  namespace: otel-demo
spec:
  type: NodePort
  ports:
    - port: 8080
      targetPort: 8080
      nodePort: 30080
      name: tcp-service
  selector:
    app.kubernetes.io/name: frontend-proxy
---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: otel-demo
spec:
  type: NodePort
  ports:
    - port: 80
      targetPort: 3000
      nodePort: 30300
      name: service
  selector:
    app.kubernetes.io/name: grafana
---
apiVersion: v1
kind: Service
metadata:
  name: jaeger-query
  namespace: otel-demo
spec:
  type: NodePort
  ports:
    - port: 16686
      targetPort: 16686
      nodePort: 30686
      name: http-query
  selector:
    app.kubernetes.io/name: jaeger
    app.kubernetes.io/component: service-query
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: otel-demo
spec:
  type: NodePort
  ports:
    - port: 9090
      targetPort: 9090
      nodePort: 30090
      name: http
  selector:
    app.kubernetes.io/name: prometheus
